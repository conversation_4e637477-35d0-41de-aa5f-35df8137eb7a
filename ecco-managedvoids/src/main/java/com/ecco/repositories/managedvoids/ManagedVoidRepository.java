package com.ecco.repositories.managedvoids;

import com.ecco.dom.managedvoids.ManagedVoid;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.Optional;

public interface ManagedVoidRepository extends QuerydslPredicateExecutor<ManagedVoid>, CrudRepositoryWithFindOne<ManagedVoid, Integer> {

    @Query("SELECT i.serviceRecipient.id from ManagedVoid i where id = ?1")
    int getServiceRecipientId(int voidId);

    Optional<ManagedVoid> findManagedVoidByServiceRecipientId(Integer id);
}
