package com.ecco.webApi.buildings;

import com.ecco.webApi.contacts.address.AddressViewModel;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.web.util.UriComponentsBuilder;

import com.ecco.webApi.evidence.BaseCommandViewModel;
import com.ecco.dto.AddedRemovedDto;
import com.ecco.dto.ChangeViewModel;
import java.util.List;

@NullMarked
public class BuildingCommandViewModel extends BaseCommandViewModel {

    @Nullable
    public Integer serviceRecipientId;

    public String operation;

    @Nullable
    public ChangeViewModel<String> name;

    @Nullable
    public ChangeViewModel<Boolean> disabled;

    @Nullable
    public ChangeViewModel<String> externalRef;

    @Nullable
    public ChangeViewModel<Integer> location;

    @Nullable
    public ChangeViewModel<AddressViewModel> address;

    @Nullable
    public ChangeViewModel<Integer> resourceType;

    @Nullable
    public AddedRemovedDto<ChargeCategoryCombination> chargeCategoryCombinations;

    @Nullable
    public ChangeViewModel<Integer> parentId;

    private BuildingCommandViewModel() {
        super(UriComponentsBuilder
                .fromUriString("buildings/commands/")
                .toUriString());
    }

    // Mirror buildings/commands.ts
    /**
     * @param operation "add" or "update"
     * @param serviceRecipientId null when doing add, required when doing update.
     */
    public BuildingCommandViewModel(String operation, @Nullable Integer serviceRecipientId) {
        this();
        this.operation = operation;
        this.serviceRecipientId = serviceRecipientId;
    }

    public BuildingCommandViewModel changeName(@Nullable String from, String to) {
        this.name = ChangeViewModel.create(from, to);
        return this;
    }

    public BuildingCommandViewModel changeExternalRef(@Nullable String from, @Nullable String to) {
        this.externalRef = ChangeViewModel.create(from, to);
        return this;
    }

    public static class ChargeCategoryCombination {
        @Nullable
        public Integer chargeCategoryId;
        @Nullable
        public Integer chargeNameId;

        public ChargeCategoryCombination() {}

        public ChargeCategoryCombination(@Nullable Integer chargeNameId, @Nullable Integer chargeCategoryId) {
            this.chargeNameId = chargeNameId;
            this.chargeCategoryId = chargeCategoryId;
        }
    }

    public BuildingCommandViewModel changeAddressLocationId(Integer from, @Nullable Integer to) {
        this.location = ChangeViewModel.create(from, to);
        return this;
    }

    public BuildingCommandViewModel changeParentId(@Nullable Integer from, @Nullable Integer to) {
        this.parentId = ChangeViewModel.create(from, to);
        return this;
    }

    public BuildingCommandViewModel changeResourceTypeId(@Nullable Integer from, @Nullable Integer to) {
        this.resourceType = ChangeViewModel.create(from, to);
        return this;
    }

    /**
     * Sets a single charge category combination. This is a convenience method for the common case
     * where a building has only one charge category. For multiple combinations, use changeChargeCategoryCombinations.
     * NB This assumes a NULL chargeNameId.
     *
     * @param from Previous charge category ID (can be null for new buildings)
     * @param to New charge category ID (can be null to remove)
     * @return this command for method chaining
     */
    public BuildingCommandViewModel changeChargeCategoryId(@Nullable Integer from, @Nullable Integer to) {
        // Convert single charge category IDs to combinations
        List<ChargeCategoryCombination> fromCombinations = from != null ?
            List.of(new ChargeCategoryCombination(null, from)) :
            List.of();

        List<ChargeCategoryCombination> toCombinations = to != null ?
            List.of(new ChargeCategoryCombination(null, to)) :
            List.of();

        return changeChargeCategoryCombinations(fromCombinations, toCombinations);
    }

    /**
     * Changes charge category combinations using AddedRemovedDto pattern.
     * This handles adding and removing multiple charge category combinations.
     */
    public BuildingCommandViewModel changeChargeCategoryCombinations(@Nullable List<ChargeCategoryCombination> from, @Nullable List<ChargeCategoryCombination> to) {
        List<ChargeCategoryCombination> fromList = from != null ? from : List.of();
        List<ChargeCategoryCombination> toList = to != null ? to : List.of();

        // Calculate added combinations
        List<ChargeCategoryCombination> added = toList.stream()
            .filter(toItem -> fromList.stream().noneMatch(fromItem ->
                java.util.Objects.equals(fromItem.chargeCategoryId, toItem.chargeCategoryId) &&
                java.util.Objects.equals(fromItem.chargeNameId, toItem.chargeNameId)
            ))
            .toList();

        // Calculate removed combinations
        List<ChargeCategoryCombination> removed = fromList.stream()
            .filter(fromItem -> toList.stream().noneMatch(toItem ->
                java.util.Objects.equals(fromItem.chargeCategoryId, toItem.chargeCategoryId) &&
                java.util.Objects.equals(fromItem.chargeNameId, toItem.chargeNameId)
            ))
            .toList();

        // Only set the change if there are actual differences
        if (!added.isEmpty() || !removed.isEmpty()) {
            this.chargeCategoryCombinations = AddedRemovedDto.addedRemoved(
                !fromList.isEmpty() ? fromList : null,
                !added.isEmpty() ? added : null,
                !removed.isEmpty() ? removed : null
            );
        }

        return this;
    }

}
