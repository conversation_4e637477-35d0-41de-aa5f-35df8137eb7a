# Fix for ClassCastException in advancedChargesForBldgAddressChanges

## Problem
The test `advancedChargesForBldgAddressChanges` was failing with:
```
class java.util.LinkedHashMap cannot be cast to class com.ecco.buildings.dom.FixedContainer$ChargeCategoryInfo
```

## Root Cause
When <PERSON> deserializes JSON data through Hibernate's `JSONUserTypeStringToObjectMap`, it creates `LinkedHashMap` objects instead of `ChargeCategoryInfo` objects. This happens because <PERSON> doesn't have type information to properly deserialize the nested objects.

## Solution
Modified the `FixedContainer.getChargeCategoryCombinations()` method to handle both:
1. Properly deserialized `ChargeCategoryInfo` objects
2. `LinkedHashMap` objects that need to be converted to `ChargeCategoryInfo`

## Changes Made

### 1. Updated getChargeCategoryCombinations() method
- Added type checking for deserialized objects
- Handle both `ChargeCategoryInfo` and `Map` instances
- Convert `LinkedHashMap` to `ChargeCategoryInfo` when needed

### 2. Updated addChargeCategoryCombination() method
- Use `getChargeCategoryCombinations()` to handle existing data properly
- Ensures new combinations are stored correctly

### 3. Updated field type
- Changed from `Map<String, ChargeCategoryInfo[]>` to `Map<String, List<ChargeCategoryInfo>>`
- Lists are more compatible with JSON serialization/deserialization

## Testing
The fix should resolve the ClassCastException in the `advancedChargesForBldgAddressChanges` test by properly handling the deserialized JSON data regardless of whether Jackson creates `ChargeCategoryInfo` objects or `LinkedHashMap` objects.
