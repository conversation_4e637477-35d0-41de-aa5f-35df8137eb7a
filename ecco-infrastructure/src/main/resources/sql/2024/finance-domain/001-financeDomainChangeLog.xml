<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd"
        logicalFilePath="2024/finance-domain">

    <!-- HANDLES: (based on search for <createTable)
     - see main financeDomainChangeLog.xml for what tables are involved in the domain
    -->

    <changeSet id="DEV-2634-fin-hb-income-statement" author="djc">
        <createTable tableName="fin_hb_incomestatement">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="reference" type="VARCHAR(255)"/>
            <column name="issue_date" type="DATE"/>
            <column name="period_start" type="DATE"/>
            <column name="period_end" type="DATE"/>
        </createTable>
    </changeSet>

    <changeSet id="DEV-2634-fin-hb-line-item" author="djc">
        <createTable tableName="fin_hb_lineitem">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="version" type="INT"/>
            <column name="fin_hb_incomestatement_id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="claimant_first_name" type="VARCHAR(255)"/>
            <column name="claimant_last_name" type="VARCHAR(255)"/>
            <column name="claimant_ni_number" type="VARCHAR(9)"/>
            <column name="claimant_address" type="VARCHAR(1024)"/>
            <column name="income" type="DECIMAL"/>
        </createTable>
    </changeSet>

    <changeSet id="DEV-2634-fk-hbincomestatement-lineitem" author="djc">
        <addForeignKeyConstraint baseColumnNames="fin_hb_incomestatement_id"
                                 baseTableName="fin_hb_lineitem"
                                 constraintName="fk_hbincomestatement_lineitem"
                                 referencedColumnNames="id"
                                 referencedTableName="fin_hb_incomestatement"/>
    </changeSet>

    <changeSet id="DEV-2703-add-rateCardEntry-validFrom" author="nealeu">
        <addColumn tableName="fin_ratecardentries">
            <column name="validFrom" type="DATE" defaultValueDate="1970-01-01">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="DEV-2705-rename-matchingOutcomeId-chargeCategoryListDefId" author="nealeu">
        <renameColumn tableName="fin_ratecardentries" oldColumnName="matchingOutcomeId" newColumnName="chargeCategoryListDefId" columnDataType="INT"/>
    </changeSet>

    <changeSet id="DEV-2705-require-ratecard-version-with-default" author="nealeu">
        <addNotNullConstraint tableName="fin_ratecards" columnName="version" columnDataType="INT" />
        <addNotNullConstraint tableName="fin_ratecardentries" columnName="version" columnDataType="INT" />
    </changeSet>

    <changeSet id="DEV-2705-add-ratecard-version-default" author="nealeu">
        <addDefaultValue tableName="fin_ratecards" columnName="version" defaultValueNumeric="0" />
        <addDefaultValue tableName="fin_ratecardentries" columnName="version" defaultValueNumeric="0" />
    </changeSet>

    <changeSet id="DEV-2705-sample-rate-cards" author="nealeu" context="acceptanceTests,data-demo-site">
        <sql>
            INSERT INTO fin_ratecards (id, name, startInstant, endInstant) VALUES
               (1, 'standard', '2023-01-01T00:00:00', null),
               (2, 'bespoke', '2023-01-01T00:00:00', null);

        </sql>
    </changeSet>
    <changeSet id="DEV-2705-add-sample-ratecard-listdefs" author="nealeu">
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, disabled, isDefault, parentId, metadata, businessKey)
            VALUES
            (204, 0, 'eventStatusRateId', 'late cancellation (flat charge)', false, false, null, '{}', '204'),
            (205, 0, 'eventStatusRateId', 'no show (flat charge)', false, false, null, '{}', '205'),
            (206, 0, 'eventStatusRateId', 'default',               false, true, null, '{}', '206'),
            (207, 0, 'rentBand',          'Band A',                false, true, null, '{}', '207'),
            (208, 0, 'rentBand',          'Band B',                false, true, null, '{}', '208'),
            (209, 0, 'rentBand',          'Band C',                false, true, null, '{}', '209'),
            (210, 0, 'rentBand',          'Band D',                false, true, null, '{}', '210'),
            (211, 0, 'rentBand',          'Band E',                false, true, null, '{}', '211'),
            (212, 0, 'serviceCharges', 'Service Charge',           false, true, null, '{}', '212'),
            (213, 0, 'serviceCharges', 'Support Charge - Level 1 (Basic)',        false, true, null, '{}', '213'),
            (214, 0, 'serviceCharges', 'Support Charge - Level 2 (Intermediate)', false, true, null, '{}', '214'),
            (215, 0, 'serviceCharges', 'Support Charge - Level 3 (Enhanced)',     false, true, null, '{}', '215'),
            (216, 0, 'serviceCharges', 'Community Alarm',                false, true, null, '{}', '216'),
            (217, 0, 'serviceCharges', 'Utilities Package',              false, true, null, '{}', '217'),
            (218, 0, 'serviceCharges', 'Independent Living Care Charge', false, true, null, '{}', '218')
            ;

        </sql>
    </changeSet>
    <changeSet id="DEV-2705-sample-rate-card-entries" author="nealeu" context="acceptanceTests,data-demo-site">
        <sql>
            INSERT INTO fin_ratecardentries (id, rateCardId, disabled, defaultEntry, chargeCategoryListDefId, chargeType,
                                             fixedCharge, unitMeasurementId, units, unitCharge) VALUES
                -- Standard rate card entries
                (1, 1, false, true, 206, 'TEMPORAL', NULL, 2, 1, 50.00), -- attended 50/hr [defaultEntry]
                (2, 1, false, false, 204, 'FIXED', 30.00, NULL, NULL, NULL), -- late cancellation 30 flat
                (3, 1, false, false, 205, 'FIXED', 50.00, NULL, NULL, NULL), -- no show 50 flat
                (4, 1, false, false, 159, 'FIXED', 0.00, NULL, NULL, NULL), -- cancelled 0
--              (5, 1, false, false, NULL, 'FIXED_TEMPORAL', 0.00, 2, 1, 20.00), -- non SBH with payElement
--              (6, 1, false, false, NULL, 'TEMPORAL', NULL, 2, 1, 24.00), -- SBH rate
--              (7, 1, false, false, 1, 'TEMPORAL', NULL, 3, 1, 35.00), -- Waking Nights 35/night
                -- Bespoke rate card entries
                (8, 2, false, false, NULL, 'FIXED', 0.00, NULL, NULL, NULL), -- framework
                (9, 2, false, false, NULL, 'FIXED', 0.00, NULL, NULL, NULL), -- individual
                -- Housing related
                (10, 1, false, false, 207, 'FIXED', 120.00, NULL, NULL, NULL), -- Band A 120 flat
                (11, 1, false, false, 208, 'FIXED', 130.00, NULL, NULL, NULL), -- Band B 130 flat
                (12, 1, false, false, 209, 'FIXED', 140.00, NULL, NULL, NULL), -- Band C 140 flat
                (13, 1, false, false, 210, 'FIXED', 150.00, NULL, NULL, NULL), -- Band D 150 flat
                (14, 1, false, false, 211, 'FIXED', 160.00, NULL, NULL, NULL), -- Band E 160 flat
                (15, 1, false, false, 212, 'FIXED', 36.00, NULL, NULL, NULL), -- Service Charge
                (16, 1, false, false, 213 ,'TEMPORAL', NULL, 2, 1, 10.93), -- Support Charge - Level 1 (Basic)
                (17, 1, false, false, 214, 'TEMPORAL', NULL, 2, 1, 14.14), -- Support Charge - Level 2 (Intermediate)
                (18, 1, false, false, 215, 'TEMPORAL', NULL, 2, 1, 17.49), -- Support Charge - Level 3 (Enhanced)
                (19, 1, false, false, 216, 'FIXED', 22.00, NULL, NULL, NULL), -- Community Alarm
                (20, 1, false, false, 217, 'FIXED', 30.00, NULL, NULL, NULL), -- Utilities Package
                (21, 1, false, false, 218, 'FIXED', 73.35, NULL, NULL, NULL); -- Independent Living Care Charge
        </sql>
    </changeSet>

    <changeSet id="DEV-2705-default-contract" author="nealeu" context="acceptanceTests,data-demo-site">
        <sql>
            INSERT INTO servicerecipients (id, version, discriminator_orm, created, dataProtectionSignatureId, consentSignatureId, latestClientStatusId, currentTaskId, currentTaskIndex, nextDueSlaTaskId, nextSlaDueDate, serviceAllocationId, agreementSignatureId, agreement2SignatureId, agreement3SignatureId, latestClientStatusDateTime, agreement4SignatureId, agreement5SignatureId, agreement6SignatureId, agreement7SignatureId, agreement8SignatureId, agreement9SignatureId, agreement10SignatureId)
            VALUES (52, 0, 'cont', '2023-11-20 09:00:00', null, null, null, null, 0, null, null, -300, null, null, null, null, null, null, null, null, null, null, null);
            insert into fin_contracts (id, version, name, startInstant, endInstant, PONumbers, agreedCharge, contractTypeId, serviceRecipientId)
            values (1, 0, 'default', '2023-11-20', null, null, null, 203, 52);
            INSERT INTO fin_contracts_ratecards (contractId, rateCardId) values (1, 1);
        </sql>
    </changeSet>

    <changeSet id="DEV-2705-add-month-unitofmeasurement" author="nealeu">
        <insert tableName="fin_unitofmeasurements">
            <column name="id" valueNumeric="4"/>
            <column name="name" value="month"/>
            <column name="unitMeasurement" value="MONTH"/>
            <column name="units" valueNumeric="1"/>
            <column name="version" valueNumeric="0"/>
        </insert>
    </changeSet>

    <changeSet id="DEV-2705-update-rent-band-charge-types" author="Augment-AI-plus-nealeu"  context="acceptanceTests,data-demo-site">
        <comment>Update rent band entries from FIXED to TEMPORAL with 7-day charge</comment>
        <sql>
            -- Update Band A (ID 10)
            UPDATE fin_ratecardentries
            SET chargeType = 'TEMPORAL',
                unitMeasurementId = 3,
                units = 7,
                unitCharge = 120.00,
                fixedCharge = NULL
            WHERE id = 10;

            -- Update Band B (ID 11)
            UPDATE fin_ratecardentries
            SET chargeType = 'TEMPORAL',
                unitMeasurementId = 3,
                units = 7,
                unitCharge = 130.00,
                fixedCharge = NULL
            WHERE id = 11;

            -- Update Band C (ID 12)
            UPDATE fin_ratecardentries
            SET chargeType = 'TEMPORAL',
                unitMeasurementId = 3,
                units = 7,
                unitCharge = 140.00,
                fixedCharge = NULL
            WHERE id = 12;

            -- Update Band D (ID 13)
            UPDATE fin_ratecardentries
            SET chargeType = 'TEMPORAL',
                unitMeasurementId = 3,
                units = 7,
                unitCharge = 150.00,
                fixedCharge = NULL
            WHERE id = 13;

            -- Update Band E (ID 14)
            UPDATE fin_ratecardentries
            SET chargeType = 'TEMPORAL',
                unitMeasurementId = 3,
                units = 7,
                unitCharge = 160.00,
                fixedCharge = NULL
            WHERE id = 14;

            -- Update Community Alarm (ID 19)
            UPDATE fin_ratecardentries
            SET chargeType = 'TEMPORAL',
                unitMeasurementId = 4,
                units = 7,
                unitCharge = 22.00,
                fixedCharge = NULL
            WHERE id = 19;

            -- Update Utilities Package (ID 20)
            UPDATE fin_ratecardentries
            SET chargeType = 'TEMPORAL',
                unitMeasurementId = 4,
                units = 7,
                unitCharge = 30.00,
                fixedCharge = NULL
            WHERE id = 20;

            -- Update Independent Living Care Charge (ID 21)
            UPDATE fin_ratecardentries
            SET chargeType = 'TEMPORAL',
                unitMeasurementId = 4,
                units = 7,
                unitCharge = 73.35,
                fixedCharge = NULL
            WHERE id = 21;
        </sql>
    </changeSet>

    <!-- if we have a contract, we need a chargeNameId, regardless of context acceptanceTests,data-demo-site -->
    <changeSet id="DEV-2725-add-charge-type" author="adamjhamer" context="!acceptanceTests AND !data-demo-site">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="1">select count(1) from fin_contracts</sqlCheck>
        </preConditions>
        <sql>
            INSERT INTO cfg_list_definitions (id, version, listName, name, disabled, isDefault, parentId, metadata, businessKey)
            VALUES
                (219, 0, 'chargeNames', 'service charges', false, false, null, '{}', '219');
        </sql>
    </changeSet>

    <!-- every rate card needs a chargeNameId - we default to 'service charges' -->
    <changeSet id="DEV-2725-add-rateCard-chargeName" author="adamjhamer">
        <addColumn tableName="fin_ratecards">
            <column name="chargeNameId" type="INT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <update tableName="fin_ratecards">
            <column name="chargeNameId" valueNumeric="219"/>
        </update>
        <addNotNullConstraint tableName="fin_ratecards" columnName="chargeNameId" columnDataType="INT"/>
    </changeSet>

</databaseChangeLog>