
ROLE_ security is applied in the code in various places. Also, ROLE_s are collected from the database to determine access to various menu items. See the `groups` and `group_authorities` table for what roles belong to what groups.

NB we also maintain a simple file in dropbox designed for customers on the hierarchy levels: ecco-shared\eccoVideos\SecurityHierarchyGuide.docx.


ROLE_ security in ecco CODE
----------------------------

These roles are based around who the user is:
- `ROLE_USER` embedded in many places, equivalent to IS_AUTHENTICATED, simply to show whether logged in or not
- `ROLE_SYSADMIN` access to '/admin/' urls; outcomes flow;
- `ROLE_ADMIN` can delete entities (where available - typically in settings)
- `ROLE_STAFF` members form part of the 'staff' lists; displays signature box (where configured); can load a referral; can access/edit the 'referral aspects'; find referrals (incl. search clients in new referral); referral comments; referral upload
- `ROLE_CLIENT` capture client signature; access to support plan (defined in webflow)
- `ROLE_COMMISSIONER` access to referral list (according to acls) and support (defined in webflow) but read only


These are roles which give us flexibility in applying relevant access to different users avoiding the direct link to who they are (as above):
- `ROLE_ADMINLOGIN` access to individual logins (defined in webflow)
- `ROLE_ADMINLOGINLDAP` manage acls
- `ROLE_HR` (no code restriction - just menuitem restriction)
- `ROLE_SWITCHUSER` can switch users on the logins page


These are roles for which we needed greater control to specific areas, again to provide flexibility to different users:
- `ROLE_ADMINREFERRAL` can alter 'system dates' on the referral; can set an 'id' for the client; can edit comments on support; can delete attachments
- `ROLE_EDITREFERRAL` can edit 'details of referral' and edit 'initial assessment'
- `ROLE_NEWREFERRAL` can create a new referral
- `ROLE_INTERVIEWER` access to 'initial assessment'
- `ROLE_AUDIT` access the audits


ROLE_ security in ecco `menuitem`
----------------------------------

In summary, currently in the menuitem table, we have the following access:

- `ROLE_ADMIN` welcome reports menu; welcome settings menu; reports 'submissions' menu; settings 'lists' menu; settings 'user audit' menu; settings 'logo' upload
- `ROLE_STAFF` all welcome menus except hr, reports, settings
- `ROLE_HR` hr menu; reports - 'hr' menu
- `ROLE_LOGIN` settings - 'logins' menu
- `ROLE_ADMINLOGINLDAP` settings - 'ldap mapping' menu
- `ROLE_CLIENT` support plan, calendar
- `ROLE_COMMISSIONER` referrals menu; support plan menu; reports menu
