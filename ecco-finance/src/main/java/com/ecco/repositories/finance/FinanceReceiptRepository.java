package com.ecco.repositories.finance;

import com.ecco.dom.finance.FinanceReceipt;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

public interface FinanceReceiptRepository extends QuerydslPredicateExecutor<FinanceReceipt>, CrudRepositoryWithFindOne<FinanceReceipt, Integer> {

    //List<FinanceReceipt> findByServiceRecipientIdOrderByReceivedDateDesc(int serviceRecipientId);

}
