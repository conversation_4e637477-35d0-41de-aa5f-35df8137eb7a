package com.ecco.config.finance;

import com.ecco.finance.webApi.dto.FinanceChargeCalculation;
import com.ecco.repositories.finance.FinanceReceiptRepository;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration(proxyBeanMethods = false)
@EnableJpaRepositories(basePackageClasses = FinanceReceiptRepository.class)
@ComponentScan(basePackageClasses = FinanceChargeCalculation.class)
public class FinanceConfig {

}
