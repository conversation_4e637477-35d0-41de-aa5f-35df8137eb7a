package com.ecco.finance.webApi.dto;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.hateoas.RepresentationModel;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * View model, designed for JSON serialization.
 * @since 13/10/2016
 */
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ClientSalesInvoiceResource extends RepresentationModel<ClientSalesInvoiceResource> implements SalesInvoice {
    private Integer serviceRecipientId;
    private Integer invoiceId;
    private LocalDate invoiceDate;
    private Status status;
    private BigDecimal amount;

    public ClientSalesInvoiceResource(Integer serviceRecipientId, Integer invoiceId, LocalDate invoiceDate, Status status, BigDecimal amount) {
        this.serviceRecipientId = serviceRecipientId;
        this.invoiceId = invoiceId;
        this.invoiceDate = invoiceDate;
        this.status = status;
        this.amount = amount;
    }

    public Integer getServiceRecipientId() { return serviceRecipientId; }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    @Override
    public LocalDate getInvoiceDate() {
        return invoiceDate;
    }

    @Override
    public Status getStatus() {
        return status;
    }

    @Override
    public BigDecimal getAmount() {
        return amount;
    }
}
