package com.ecco.finance.webApi.dto;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.hateoas.RepresentationModel;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


/**
 * Similar in purpose to ClientSalesRotaInvoiceDetailResourceAssembler, except for service charges.
 * This, however, is generated from calculations and not a CRUD implementation.
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Data
public class ClientSalesChargeInvoiceDetailResource extends ClientSalesInvoiceResource {
    public static final String REL_PROFORMA_LINES = "proforma-lines";
    public static final String REL_LINES = "lines";

    private List<Line> lines;

    public ClientSalesChargeInvoiceDetailResource(Integer serviceRecipientId, LocalDate invoiceDate, List<Line> lines) {
        super(serviceRecipientId, null, invoiceDate, Status.DRAFT,
                lines.stream().reduce(BigDecimal.ZERO, (a, b) -> a.add(b.netAmount), BigDecimal::add));
        this.lines = lines;
    }

    /**
     * This class is used to persist to ClientSalesInvoiceLine and as a dto.
     */
    @EqualsAndHashCode(callSuper = true)
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    @Data
    @AllArgsConstructor
    public static class Line extends RepresentationModel<Line> {
        private Integer serviceRecipientId;
        private UUID lineUuid;
        private String type;
        private String description;
        private Integer rateCardId; // the rate card used to produce the charges
        private BigDecimal netAmount;
        private BigDecimal taxRate;

        private Integer invoiceId;
        private boolean locked;

        private Integer buildingId;
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        private LocalDateTime chargeFrom;
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        private LocalDateTime chargeTo;
    }
}
