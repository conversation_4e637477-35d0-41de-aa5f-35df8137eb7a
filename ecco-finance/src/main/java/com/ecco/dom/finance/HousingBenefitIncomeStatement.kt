package com.ecco.dom.finance

import com.ecco.infrastructure.entity.AbstractIntKeyedEntity
import java.sql.Date
import javax.persistence.*

@Entity
@Table(name = "fin_hb_incomestatement")
class HousingBenefitIncomeStatement(
    @Column(length = 255)
    val reference: String?,
    @Column(name = "issue_date")
    val issueDate: Date?,
    @Column(name = "period_start")
    val periodStart: Date?,
    @Column(name = "period_end")
    val periodEnd: Date?,
    @OneToMany(mappedBy = "incomeStatement", cascade = [CascadeType.ALL])
    val lineItems: List<HousingBenefitLineItem>,
    id: Int? = null,
) : AbstractIntKeyedEntity(id)