package com.ecco.dom.finance

import com.ecco.infrastructure.entity.AbstractIntKeyedEntity
import java.math.BigDecimal
import javax.persistence.*

@Entity
@Table(name = "fin_hb_lineitem")
class HousingBenefitLineItem(
    @Column(name = "claimant_first_name", length = 255)
    val claimantFirstName: String?,
    @Column(name = "claimant_last_name", length = 255)
    val claimantLastName: String?,
    @Column(name = "claimant_ni_number", length = 9)
    val claimantNiNumber: String?,
    @Column(name = "claimant_address", length = 1024)
    val claimantAddress: String?,
    val income: BigDecimal,
    id: Int? = null,
    @ManyToOne
    @JoinColumn(name = "fin_hb_incomestatement_id", nullable = false)
    var incomeStatement: HousingBenefitIncomeStatement? = null,
) : AbstractIntKeyedEntity(id)