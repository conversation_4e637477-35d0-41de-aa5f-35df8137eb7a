import {CommandQueue, CommandSource, UserChangeCommand} from "ecco-commands";
import * as React from "react";
import {FC, Reducer, useReducer, useMemo} from "react";
import {ModalCommandForm, useCommandSourceRegistration} from "../cmd-queue/CommandForm";
import {useGroups, useUser} from "../data/entityLoadHooks";
import {GroupDto, Messages, SessionData, UserDto} from "ecco-dto";
import {Grid, Typography} from "@eccosolutions/ecco-mui";
import {LoadingSpinner} from "../Loading";
import {createCheckBox, createTextInput} from "ecco-components-core";
import {useServicesContext} from "../ServicesContext";

const EditUserSubform: FC<{username: string}> = ({username}) => {
    const {user} = useUser(username);
    return user ? <UserSubform user={user} /> : null;
};

interface Props {
    /** Omit if creating new */
    username?: string | undefined;
    /** Callback to allow a new user form to let callee know what username was entered */
    notifyNewUsername?: ((username: string) => void) | undefined;
    show: boolean;
    setShow: (show: boolean) => void;
    afterSave?: (() => void) | undefined;
}

type State = {
    isNew: boolean;
    username: string | null;
    firstName: string | null;
    lastName: string | null;
    email: string | null;
    groups: string[];
    enabled: boolean;
    mfaRequired: boolean;
};
//type Action<K extends keyof State = keyof State> = { propertyKey: K, value: State[K] };
type Action = Partial<State>;
type UserReducer = Reducer<State, Action>;


const UserSubform: FC<{
    user?: UserDto | undefined;
    notifyNewUsername?: ((username: string) => void) | undefined;
}> = ({user, notifyNewUsername}) => {
    const {groups} = useGroups();

    // We use this fixed object to hold the state we're mutating
    const stateHolder = useMemo<{state: State}>(
        () => ({
            state: {
                isNew: !user,
                username: user ? user.username : null,
                firstName: user ? user.individual.firstName : null,
                lastName: user ? user.individual.lastName : null,
                email: user ? user.individual.email! : null,
                enabled: user ? user.enabled : true,
                mfaRequired: user ? user.mfaRequired : false,
                groups: user ? user.groups.slice() : []
            }
        }),
        []
    );

    const reducer: UserReducer = (prevState, action) => {
        stateHolder.state = {...stateHolder.state, ...action};
        return stateHolder.state;
    };

    const [state, dispatch] = useReducer<UserReducer>(reducer, stateHolder.state);

    const commandSource: CommandSource = {
        // Note: The closure will freeze whatever values are seen here, so state needs to be managed as part
        emitChangesTo: function (cmdQ: CommandQueue): void {
            const cmd = new UserChangeCommand(user ? "update" : "add", user?.userId)
                .changeUsername(user ? user.username : null, stateHolder.state.username)
                .changeFirstName(
                    user ? user.individual.firstName : null,
                    stateHolder.state.firstName
                )
                .changeLastName(user ? user.individual.lastName : null, stateHolder.state.lastName)
                .changeEmail(user ? user.individual.email! : null, stateHolder.state.email)
                .changeGroups(user ? user.groups : [], stateHolder.state.groups)
                .changeEnabled(user ? user.enabled : false, stateHolder.state.enabled)
                .changeMfaRequired(user ? user.mfaRequired : false, stateHolder.state.mfaRequired);

            if (cmd.hasChanges()) {
                cmdQ.addCommand(cmd);
            }
        },
        getErrors(): string[] {
            const errors: string[] = [];
            if (stateHolder.state.isNew) {
                if (!/^[\w-.]+$/.test(stateHolder.state.username || "")) {
                    errors.push("username can only contain characters, numbers, _, - and .");
                }
            }
            if (!stateHolder.state.firstName) {
                errors.push("first name is required");
            }
            if (!stateHolder.state.lastName) {
                errors.push("last name is required");
            }
            return errors;
        }
    };
    useCommandSourceRegistration(commandSource, () => {
        // NB the user has not been saved yet when we had this in emitChangesTo
        // meaning the setSearch in UsersList won't find the new user
        if (notifyNewUsername) {
            notifyNewUsername(stateHolder.state.username || "");
        }
    });

    function toggleGroup(name: string) {
        const i = state.groups.indexOf(name);
        if (i < 0) {
            state.groups.push(name);
        } else {
            state.groups.splice(i, 1);
        }
        dispatch({groups: state.groups});
    }

    return (
        <Grid container>
            <Grid item xs={6}>
                {createTextInput(
                    "username",
                    "username",
                    state.username,
                    value => dispatch({username: value}),
                    "text",
                    {
                        disabled: user != null,
                        required: true,
                        helperText: "only letters, digits, - and . allowed"
                    },
                    (_required, s) => (s && /^[\w-.]+$/.test(s) ? "success" : "error")
                )}
            </Grid>
            <Grid item xs={6}>
                {createTextInput(
                    "firstName",
                    "first name",
                    state.firstName,
                    value => dispatch({firstName: value}),
                    "text",
                    {required: true}
                )}
            </Grid>
            <Grid item xs={6}>
                {createTextInput(
                    "lastName",
                    "last name",
                    state.lastName,
                    value => dispatch({lastName: value}),
                    "text",
                    {required: true}
                )}
            </Grid>
            <Grid item xs={6}>
                {createTextInput(
                    "email",
                    "email",
                    state.email,
                    value => dispatch({email: value}),
                    "text"
                )}
            </Grid>
            <Grid item xs={12}>
                {createCheckBox("enabled", "account enabled", state.enabled, () =>
                    dispatch({enabled: !state.enabled})
                )}
                {createCheckBox("mfaRequired", "mfa required", state.mfaRequired, () =>
                    dispatch({mfaRequired: !state.mfaRequired})
                )}
            </Grid>
            <Grid item xs={12}>
                <Typography variant="h6">top level groups</Typography>
                {groups &&
                    groups
                        .filter(g => g.authorities.indexOf("ROLE_USER") >= 0)
                        .map(g =>
                            createCheckBox(g.name, g.name, state.groups.indexOf(g.name) >= 0, () =>
                                toggleGroup(g.name)
                            )
                        )}
            </Grid>
            <Grid item xs={12}>
                <Typography variant="h6">extra permissions</Typography>
                {groups &&
                    findExtraPermissions(groups).map(g =>
                        createCheckBox(g.name, g.name, state.groups.indexOf(g.name) >= 0, () =>
                            toggleGroup(g.name)
                        )
                    )}
            </Grid>
            <Grid item xs={12}>
                <Typography variant="h6">limiting permissions</Typography>

                {groups &&
                    findLimitingPermissions(groups).map(g =>
                        createCheckBox(g.name, g.name, state.groups.indexOf(g.name) >= 0, () =>
                            toggleGroup(g.name)
                        )
                    )}
            </Grid>
        </Grid>
    );
};

function findExtraPermissions(groups: GroupDto[]): GroupDto[] {
    return groups.filter(g => g.authorities.indexOf("ROLE_USER") < 0)
        .filter(g => findLimitingPermissions([g]).length == 0)
}
function findLimitingPermissions(groups: GroupDto[]): GroupDto[] {
    return groups.filter(g =>
        g.authorities.some(r => SessionData.hasRoleFileLimitingList().indexOf(r) > -1)
    );
}

function authAsEnglish(auth: string, messages: Messages) {
    const projectsLabel = messages["projects"];
    switch (auth) {
        case "ROLE_DEMO": // was used to limit features on a demo site and replace menu item with video or "contact us"
        case "ROLE_USER": // Implied by the displayed groupings on the form
            return null;
        case "ROLE_AAA":
            return <li>allows access to all services/${`projectsLabel`}</li>;
        case "ROLE_CLIENT":
            return (
                <li>
                    allows a client to access their file - <em>do not use this for staff</em>&nbsp;
                    (speak to us if you would like to use this access)
                </li>
            );
        case "ROLE_COMMISSIONER":
            return (
                <li>
                    allows a commissioner restricted read-only access -{" "}
                    <em>do not use this for staff</em>&nbsp; (speak to us if you would like to use
                    this access)
                </li>
            );
        case "ROLE_HR":
            return <li>HR access</li>;
        case "ROLE_REPORTS":
            return <li>reports access</li>;
        case "ROLE_ADMINBUILDING":
            return <li>buildings administrator</li>;
        case "ROLE_ADMINCALENDAR":
            return <li>calendar administrator (currently can delete legacy appointments)</li>;
        case "ROLE_ADMINROTA":
            return <li>rota access (best used via 'manager')</li>;
        case "ROLE_HR-VOLUNTEER": // Intended to indicate someone with some access as a volunteer vs permanent staff (I think)
            return (
                <li>
                    Indicates system should restrict HR access to only seeing staff overview - no
                    ability to edit
                </li>
            );
        case "ROLE_ADMINGROUPSUPPORT":
            return <li>group support activities administrator</li>;
        case "ROLE_OVERVIEWREFERRAL":
            return <li>only show the 'overview' tab on a client file</li>;
        case "ROLE_OVERVIEWREFERRAL_TASKS":
            return <li>only show the 'tasks' tab on a client file</li>;
        case "ROLE_OVERVIEWREFERRAL_TASKS_1":
            return <li>only show the 'tasks' tab on a client file with signed agreements</li>;
        case "ROLE_OVERVIEWREFERRAL_HIST":
            return <li>only show the support history tab on a client file</li>;
        case "ROLE_ADMINREFERRAL":
            /* ts:  see 'manager notes' / menu:
             jsp: edit clientId, delete attachments, undo close off, 'tasks' if secured, audit-history
            'support notes' are edited using ROLE_ADMINEVIDENCE */
            return (
                <li>
                    referral administrator (can edit system dates and id's; create future-dated
                    work;&nbsp; delete unattached attachments; undo a closed referral; see the
                    'audits')
                </li>
            );
        case "ROLE_NEWREFERRAL":
            return <li>new referral</li>;
        case "ROLE_EDITREFERRAL":
            return <li>edit 'details of referral' and see/edit 'initial assessment'</li>;
        case "ROLE_FUNDINGREFERRAL":
            return <li>edit 'funding'</li>;
        case "ROLE_DELETEREFERRAL":
            return <li>delete referrals (once requested)</li>;
        case "ROLE_STARTREFERRAL":
            return <li>edit 'start'</li>;
        case "ROLE_CLOSEREFERRAL":
            return <li>close referrals</li>;
        case "ROLE_INTERVIEWER":
            return null; // can be an interviewer - but interviews now pick up ROLE_STAFF?
        case "ROLE_STAFF":
            return (
                <li>
                    staff access (forms part of the 'staff' lists and can access/edit referrals)
                </li>
            );
        case "ROLE_OFFLINE":
            return (
                <li>users who can use ecco in 'offline' mode (offline is an optional module)</li>
            );
        case "ROLE_CALENDARADMIN":
            return <li>delete ad-hoc calendar entries</li>;
        case "ROLE_ADMIN":
            return (
                <>
                    <li>global config administrator</li>
                    <li>hard delete referrals & clients</li>
                </>
            );
        case "ROLE_AUDIT":
            return <li>see audits</li>;
        case "ROLE_SOFTDELETE":
            return <li>request/unrequest referral deletion</li>;
        case "ROLE_ADMINEVIDENCE":
            return <li>delete evidence and edit signed evidence</li>;
        case "ROLE_EDITEVIDENCE":
            return <li>edit evidence, once its created</li>;
        case "ROLE_EDITEVIDENCEFLAG":
            return <li>amend evidence flags</li>;
        case "ROLE_EDITEVIDENCEPARKED":
            return <li>show/amend parked evidence</li>;
        case "ROLE_EDITTASK":
            return <li>edit tasks</li>;
        case "ROLE_EDITTASK_DUE":
            return <li>edit task due dates</li>;
        case "ROLE_ADMINLOGIN":
            return (
                <>
                    <li>manage user logins</li>
                    <li>reports access 'login audits' (also requires access to reports)</li>
                </>
            );
        case "ROLE_SYSADMIN":
            return (
                <li>
                    ecco only - certain user management, config, task, guidance notes and admin
                    services
                </li>
            );
        case "ROLE_SWITCHUSER":
            return <li>impersonate logins permission (also requires 'useradmin')</li>;
        case "ROLE_EVANGELIST":
            return (
                <li>primary contact for ecco to filter and manage the communication with ecco</li>
            );
        case "ROLE_CARER":
            return <li>provide care-worker focused user interface</li>;
        case "ROLE_SECURITY":
            return (
                <li>
                    provide security-worker focused user interface (currently just my/team, no
                    client file access)
                </li>
            );
        case "ROLE_FINANCE":
            return <li>allows access to payroll and invoicing focused functions</li>;
        default:
            return <li>[{auth}]</li>;
        case "ROLE_MANAGEDVOID":
            return <li>managed voids access (assigned to me)</li>;
        case "ROLE_ADMINMANAGEDVOID":
            return <li>managed voids access (all)</li>;
        case "ROLE_REPAIRS":
            return <li>repairs access (assigned to me)</li>;
        case "ROLE_ADMINREPAIR":
            return <li>repairs access (all)</li>;
        case "ROLE_INCIDENTS":
            return <li>incidents access (assigned to me)</li>;
        case "ROLE_TEAMINCIDENTS":
            return <li>incidents access (team)</li>;
        case "ROLE_ADMININCIDENT":
            return <li>incidents access (all)</li>;
    }
}

const UserHelp = () => {
    const {groups, loading} = useGroups();
    const messages = useServicesContext().sessionData.getMessages();
    return loading ? (
        <LoadingSpinner />
    ) : (
        <>
            <p>
                ecco has templates of what access rights a user can have as defined below in{" "}
                <em>groups</em>. Your system has the following but you can contact us to define new
                ones or change them:
            </p>
            {groups!.map(group => (
                <p>
                    <h5>{group.name}</h5>
                    <ul>{group.authorities.map(a => authAsEnglish(a, messages))}</ul>
                </p>
            ))}
        </>
    );
};




/* @Exemplar */
export const UserForm: FC<Props> = ({username, show, setShow, notifyNewUsername, afterSave}) => {
    return (
        <ModalCommandForm
            title={username ? `editing user ${username}` : "new user"}
            action={username ? "update" : "save"}
            show={show}
            setShow={setShow}
            afterSave={() => {
                afterSave && afterSave();
            }}
            helpTitle="managing users"
            helpContent={<UserHelp />}
        >
            {username ? (
                <EditUserSubform username={username} />
            ) : (
                <UserSubform notifyNewUsername={notifyNewUsername} />
            )}
            {/*We could have multiple subforms on different tabs here */}
        </ModalCommandForm>
    );
};
