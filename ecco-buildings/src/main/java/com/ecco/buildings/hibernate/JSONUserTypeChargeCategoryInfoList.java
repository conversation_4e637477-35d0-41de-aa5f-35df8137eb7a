package com.ecco.buildings.hibernate;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.infrastructure.config.web.ConvertersConfig;
import com.ecco.infrastructure.hibernate.BaseJSONClob;
import com.ecco.infrastructure.hibernate.JSONClob;
import com.ecco.infrastructure.hibernate.JSONUserTypeToStringColumn;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Hibernate UserType specifically for handling Map<String, List<ChargeCategoryInfo>>
 * with proper type information to avoid casting issues.
 * 
 * This UserType provides <PERSON> with exact type information so it can properly
 * deserialize JSON data into ChargeCategoryInfo objects instead of LinkedHashMap objects.
 */
public class JSONUserTypeChargeCategoryInfoList extends JSO<PERSON>serTypeToStringColumn implements Serializable {

    private static final JSONClobChargeCategoryInfoMap converter = 
        new JSONClobChargeCategoryInfoMap(ConvertersConfig.getObjectMapper());

    @Override
    public JSONClob getConverter() {
        return converter;
    }

    /**
     * Specialized JSONClob for ChargeCategoryInfo maps with proper type handling
     */
    private static class JSONClobChargeCategoryInfoMap extends BaseJSONClob<Map<String, List<FixedContainer.ChargeCategoryInfo>>> {
        
        private final JavaType mapType;

        public JSONClobChargeCategoryInfoMap(ObjectMapper mapper) {
            super(mapper);
            // Construct the exact type: Map<String, List<ChargeCategoryInfo>>
            JavaType listType = mapper.getTypeFactory()
                .constructCollectionType(List.class, FixedContainer.ChargeCategoryInfo.class);
            this.mapType = mapper.getTypeFactory()
                .constructMapType(HashMap.class, String.class, listType);
        }

        @Override
        public JavaType getTargetType() {
            return mapType;
        }

        @Override
        @SuppressWarnings("unchecked")
        public Class<Map<String, List<FixedContainer.ChargeCategoryInfo>>> getReturnedClass() {
            return (Class<Map<String, List<FixedContainer.ChargeCategoryInfo>>>) (Class<?>) Map.class;
        }
    }
}
